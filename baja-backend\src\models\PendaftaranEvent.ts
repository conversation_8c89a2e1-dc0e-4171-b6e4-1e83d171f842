import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { PendaftaranEvent as PendaftaranEventInterface } from '../types';
import Event from './Event';
import Kontingen from './Kontingen';

interface PendaftaranEventCreationAttributes extends Optional<PendaftaranEventInterface, 'id' | 'created_at' | 'updated_at'> {}

class PendaftaranEvent extends Model<PendaftaranEventInterface, PendaftaranEventCreationAttributes> implements PendaftaranEventInterface {
  public id!: number;
  public id_event!: number;
  public id_kontingen!: number;
  public status!: 'pending' | 'approved' | 'rejected';
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

PendaftaranEvent.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    id_event: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Event,
        key: 'id',
      },
    },
    id_kontingen: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Kontingen,
        key: 'id',
      },
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'pendaftaran_event',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Associations will be set up in models/index.ts

export default PendaftaranEvent;
