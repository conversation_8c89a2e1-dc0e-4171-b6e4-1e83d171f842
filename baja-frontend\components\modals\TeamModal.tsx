'use client';

import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface TeamModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  team?: {
    id: number;
    name: string;
    negara: string;
    provinsi: string;
    kabupaten_kota: string;
  } | null;
}

const TeamModal: React.FC<TeamModalProps> = ({ isOpen, onClose, onSuccess, team }) => {
  const [formData, setFormData] = useState({
    name: '',
    negara: '',
    provinsi: '',
    kabupaten_kota: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (team) {
      setFormData({
        name: team.name,
        negara: team.negara,
        provinsi: team.provinsi,
        kabupaten_kota: team.kabupaten_kota,
      });
    } else {
      setFormData({
        name: '',
        negara: '',
        provinsi: '',
        kabupaten_kota: '',
      });
    }
    setError('');
  }, [team, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const url = team 
        ? `${process.env.NEXT_PUBLIC_API_URL}/api/v1/kontingen/${team.id}`
        : `${process.env.NEXT_PUBLIC_API_URL}/api/v1/kontingen`;
      
      const method = team ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onSuccess();
        onClose();
      } else {
        setError(data.message || 'Failed to save team');
      }
    } catch (error) {
      console.error('Error saving team:', error);
      setError('Failed to save team');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={team ? 'Edit Team' : 'Create Team'}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Team Name *
          </label>
          <Input
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            placeholder="Enter team name"
            required
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="negara" className="block text-sm font-medium text-gray-300 mb-2">
            Country *
          </label>
          <Input
            id="negara"
            name="negara"
            type="text"
            value={formData.negara}
            onChange={handleChange}
            placeholder="Enter country"
            required
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="provinsi" className="block text-sm font-medium text-gray-300 mb-2">
            Province *
          </label>
          <Input
            id="provinsi"
            name="provinsi"
            type="text"
            value={formData.provinsi}
            onChange={handleChange}
            placeholder="Enter province"
            required
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="kabupaten_kota" className="block text-sm font-medium text-gray-300 mb-2">
            City/Regency *
          </label>
          <Input
            id="kabupaten_kota"
            name="kabupaten_kota"
            type="text"
            value={formData.kabupaten_kota}
            onChange={handleChange}
            placeholder="Enter city or regency"
            required
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-gold-500 hover:bg-gold-600 text-black"
            disabled={loading}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {team ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              team ? 'Update Team' : 'Create Team'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default TeamModal;
