// Import all models
import User from './User';
import Event from './Event';
import Kontingen from './Kontingen';
import Atlet from './Atlet';
import Official from './Official';
import Paket from './Paket';
import Gallery from './Gallery';
import PendaftaranEvent from './PendaftaranEvent';

// Setup associations
const setupAssociations = () => {
  // User associations
  User.hasMany(Event, { foreignKey: 'id_user', as: 'userEvents' });
  User.hasMany(Kontingen, { foreignKey: 'id_user', as: 'userKontingen' });
  User.hasMany(Atlet, { foreignKey: 'id_user', as: 'userAtlet' });

  // Event associations
  Event.belongsTo(User, { foreignKey: 'id_user', as: 'eventUser' });
  Event.hasMany(PendaftaranEvent, { foreignKey: 'id_event', as: 'eventPendaftaran' });

  // Kontingen associations
  Kontingen.belongsTo(User, { foreignKey: 'id_user', as: 'kontingenUser' });
  Kontingen.hasMany(Atlet, { foreignKey: 'id_kontingen', as: 'kontingenAtlet' });
  Kontingen.hasMany(Official, { foreignKey: 'id_kontingen', as: 'kontingenOfficials' });
  Kontingen.hasMany(PendaftaranEvent, { foreignKey: 'id_kontingen', as: 'kontingenPendaftaran' });

  // Atlet associations
  Atlet.belongsTo(User, { foreignKey: 'id_user', as: 'atletUser' });
  Atlet.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'atletKontingen' });

  // Official associations
  Official.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'officialKontingen' });

  // PendaftaranEvent associations
  PendaftaranEvent.belongsTo(Event, { foreignKey: 'id_event', as: 'pendaftaranEvent' });
  PendaftaranEvent.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'pendaftaranKontingen' });
};

// Call setup associations
setupAssociations();

export {
  User,
  Event,
  Kontingen,
  Atlet,
  Official,
  Paket,
  Gallery,
  PendaftaranEvent,
};

export default {
  User,
  Event,
  Kontingen,
  Atlet,
  Official,
  Paket,
  Gallery,
  PendaftaranEvent,
};
