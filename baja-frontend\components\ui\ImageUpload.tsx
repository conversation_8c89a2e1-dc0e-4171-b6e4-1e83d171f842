'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import Button from './Button';
import Spinner from './Spinner';

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  onImageUpload?: (file: File) => Promise<void>;
  currentImageUrl?: string;
  placeholder?: string;
  maxSize?: number; // in MB
  acceptedFormats?: string[];
  className?: string;
  disabled?: boolean;
  showPreview?: boolean;
  uploadButtonText?: string;
  selectButtonText?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  onImageUpload,
  currentImageUrl,
  placeholder = "Click to select an image",
  maxSize = 5,
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png'],
  className = '',
  disabled = false,
  showPreview = true,
  uploadButtonText = "Upload",
  selectButtonText = "Select Image"
}) => {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset error
    setError(null);

    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      setError(`Please select a valid image file (${acceptedFormats.join(', ')})`);
      return;
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`);
      return;
    }

    setSelectedImage(file);
    onImageSelect(file);

    // Create preview URL
    if (showPreview) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleUpload = async () => {
    if (!selectedImage || !onImageUpload) return;

    setIsUploading(true);
    setError(null);

    try {
      await onImageUpload(selectedImage);
      setSelectedImage(null);
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setPreviewUrl(currentImageUrl || null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className} relative`}>
      {/* Preview Area */}
      {showPreview && (
        <div className="relative">
          {previewUrl ? (
            <div className="relative w-full h-48 border-2 border-gray-300 rounded-lg overflow-hidden">
              <Image
                src={previewUrl}
                alt="Preview"
                fill
                className="object-cover"
              />
              {/* Loading Overlay for Upload */}
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="bg-white rounded-lg p-4 flex flex-col items-center space-y-2">
                    <Spinner size="md" />
                    <p className="text-sm text-gray-700">Uploading...</p>
                  </div>
                </div>
              )}
              {selectedImage && (
                <div className="absolute top-2 right-2">
                  <Button
                    type="button"
                    onClick={handleRemoveImage}
                    className="bg-red-500 hover:bg-red-600 text-white p-1 rounded-full text-xs"
                    disabled={disabled || isUploading}
                  >
                    ✕
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div
              className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={handleSelectClick}
            >
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="mt-2 text-sm text-gray-600">{placeholder}</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          type="button"
          onClick={handleSelectClick}
          disabled={disabled || isUploading}
          className="flex-1"
        >
          {selectButtonText}
        </Button>

        {selectedImage && onImageUpload && (
          <Button
            type="button"
            onClick={handleUpload}
            disabled={disabled || isUploading}
            className="flex-1 bg-green-600 hover:bg-green-700"
            loading={isUploading}
          >
            {isUploading ? 'Uploading...' : uploadButtonText}
          </Button>
        )}
      </div>

      {/* File Info */}
      {selectedImage && (
        <div className="text-sm text-gray-600">
          <p>Selected: {selectedImage.name}</p>
          <p>Size: {(selectedImage.size / 1024 / 1024).toFixed(2)} MB</p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
