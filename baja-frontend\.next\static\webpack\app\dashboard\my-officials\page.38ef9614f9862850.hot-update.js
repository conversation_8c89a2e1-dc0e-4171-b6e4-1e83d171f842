"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/my-officials/page",{

/***/ "(app-pages-browser)/./components/ui/Table.tsx":
/*!*********************************!*\
  !*** ./components/ui/Table.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Table: function() { return /* binding */ Table; },\n/* harmony export */   TableBody: function() { return /* binding */ TableBody; },\n/* harmony export */   TableCell: function() { return /* binding */ TableCell; },\n/* harmony export */   TableHead: function() { return /* binding */ TableHead; },\n/* harmony export */   TableHeader: function() { return /* binding */ TableHeader; },\n/* harmony export */   TableRow: function() { return /* binding */ TableRow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Table,TableHeader,TableBody,TableRow,TableHead,TableCell auto */ \n\nconst Table = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full overflow-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            ref: ref,\n            className: cn(\"w-full caption-bottom text-sm\", className),\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Table;\nconst TableHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c2 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n        ref: ref,\n        className: cn(\"[&_tr]:border-b\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = TableHeader;\nconst TableBody = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c4 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n        ref: ref,\n        className: cn(\"[&_tr:last-child]:border-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = TableBody;\nconst TableRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c6 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n        ref: ref,\n        className: cn(\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n});\n_c7 = TableRow;\nconst TableHead = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c8 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n        ref: ref,\n        className: cn(\"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n});\n_c9 = TableHead;\nconst TableCell = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c10 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n        ref: ref,\n        className: cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\baja\\\\baja_apps 2\\\\baja_apps\\\\baja-frontend\\\\components\\\\ui\\\\Table.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n});\n_c11 = TableCell;\nTable.displayName = \"Table\";\nTableHeader.displayName = \"TableHeader\";\nTableBody.displayName = \"TableBody\";\nTableRow.displayName = \"TableRow\";\nTableHead.displayName = \"TableHead\";\nTableCell.displayName = \"TableCell\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Table$React.forwardRef\");\n$RefreshReg$(_c1, \"Table\");\n$RefreshReg$(_c2, \"TableHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"TableHeader\");\n$RefreshReg$(_c4, \"TableBody$React.forwardRef\");\n$RefreshReg$(_c5, \"TableBody\");\n$RefreshReg$(_c6, \"TableRow$React.forwardRef\");\n$RefreshReg$(_c7, \"TableRow\");\n$RefreshReg$(_c8, \"TableHead$React.forwardRef\");\n$RefreshReg$(_c9, \"TableHead\");\n$RefreshReg$(_c10, \"TableCell$React.forwardRef\");\n$RefreshReg$(_c11, \"TableCell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/Table.tsx\n"));

/***/ })

});