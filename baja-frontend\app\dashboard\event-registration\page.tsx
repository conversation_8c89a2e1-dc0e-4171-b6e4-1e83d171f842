'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import { 
  CalendarDaysIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  created_at: string;
}

interface Registration {
  id: number;
  id_event: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  pendaftaranEvent: Event;
}

const EventRegistrationPage = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [registering, setRegistering] = useState<number | null>(null);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'available' | 'registered'>('available');

  useEffect(() => {
    fetchEvents();
    fetchRegistrations();
  }, []);

  const fetchEvents = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/events`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setEvents(data.data.events || []);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      setError('Failed to fetch events');
    }
  };

  const fetchRegistrations = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/pendaftaran`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setRegistrations(data.data.pendaftaran || []);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error fetching registrations:', error);
      setError('Failed to fetch registrations');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (eventId: number) => {
    setRegistering(eventId);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/pendaftaran`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id_event: eventId }),
      });

      const data = await response.json();
      
      if (data.success) {
        fetchRegistrations();
        setActiveTab('registered');
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error registering for event:', error);
      setError('Failed to register for event');
    } finally {
      setRegistering(null);
    }
  };

  const isEventRegistered = (eventId: number) => {
    return registrations.some(reg => reg.id_event === eventId);
  };

  const getRegistrationStatus = (eventId: number) => {
    const registration = registrations.find(reg => reg.id_event === eventId);
    return registration?.status;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <Badge variant="success" className="flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="danger" className="flex items-center">
            <XCircleIcon className="h-4 w-4 mr-1" />
            Rejected
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="warning" className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            Pending
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredEvents = events.filter(event =>
    event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.lokasi.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredRegistrations = registrations.filter(reg =>
    reg.pendaftaranEvent?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reg.pendaftaranEvent?.lokasi.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Event Registration</h1>
            <p className="text-gray-400 mt-1">Register your team for events</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('available')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'available'
                ? 'bg-gold-500 text-black'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Available Events
          </button>
          <button
            onClick={() => setActiveTab('registered')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'registered'
                ? 'bg-gold-500 text-black'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            My Registrations
          </button>
        </div>

        {/* Search */}
        <Card className="p-6">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </Card>

        {/* Content */}
        {activeTab === 'available' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEvents.map((event) => (
              <Card key={event.id} className="overflow-hidden">
                {event.event_image && (
                  <div className="h-48 bg-gray-800">
                    <img
                      src={event.event_image}
                      alt={event.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-2">{event.name}</h3>
                  {event.description && (
                    <p className="text-gray-400 text-sm mb-4 line-clamp-2">{event.description}</p>
                  )}
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-400">
                      <CalendarDaysIcon className="h-4 w-4 mr-2" />
                      {new Date(event.start_date).toLocaleDateString()} - {new Date(event.end_date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <MapPinIcon className="h-4 w-4 mr-2" />
                      {event.lokasi}
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                      {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(event.biaya_registrasi)}
                    </div>
                  </div>

                  {isEventRegistered(event.id) ? (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">Status:</span>
                      {getStatusBadge(getRegistrationStatus(event.id) || '')}
                    </div>
                  ) : (
                    <Button
                      onClick={() => handleRegister(event.id)}
                      disabled={registering === event.id || event.status === 'completed'}
                      className="w-full bg-gold-500 hover:bg-gold-600 text-black"
                    >
                      {registering === event.id ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Registering...
                        </>
                      ) : (
                        <>
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Register
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredRegistrations.map((registration) => (
              <Card key={registration.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {registration.pendaftaranEvent?.name}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-400">
                        <CalendarDaysIcon className="h-4 w-4 mr-2" />
                        {new Date(registration.pendaftaranEvent?.start_date).toLocaleDateString()} - {new Date(registration.pendaftaranEvent?.end_date).toLocaleDateString()}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <MapPinIcon className="h-4 w-4 mr-2" />
                        {registration.pendaftaranEvent?.lokasi}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                        {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(registration.pendaftaranEvent?.biaya_registrasi || 0)}
                      </div>
                    </div>
                    <p className="text-sm text-gray-400">
                      Registered on: {new Date(registration.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="ml-4">
                    {getStatusBadge(registration.status)}
                  </div>
                </div>
              </Card>
            ))}
            {filteredRegistrations.length === 0 && (
              <Card className="p-8 text-center">
                <CalendarDaysIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No Registrations</h3>
                <p className="text-gray-400">You haven't registered for any events yet.</p>
              </Card>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default EventRegistrationPage;
