'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import OfficialModal from '@/components/modals/OfficialModal';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  PencilIcon, 
  TrashIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface Official {
  id: number;
  profile?: string;
  name: string;
  no_hp: string;
  alamat: string;
  agama: string;
  jenis_kelamin: 'M' | 'F';
  created_at: string;
  updated_at: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const MyOfficialsPage = () => {
  const { user } = useAuth();
  const [officials, setOfficials] = useState<Official[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [showModal, setShowModal] = useState(false);
  const [selectedOfficial, setSelectedOfficial] = useState<Official | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [officialToDelete, setOfficialToDelete] = useState<Official | null>(null);

  useEffect(() => {
    fetchOfficials();
  }, [pagination.page, searchTerm]);

  const fetchOfficials = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/official?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setOfficials(data.data.official);
        setPagination(data.data.pagination);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error fetching officials:', error);
      setError('Failed to fetch officials');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (official: Official) => {
    setSelectedOfficial(official);
    setShowModal(true);
  };

  const handleDelete = (official: Official) => {
    setOfficialToDelete(official);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!officialToDelete) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/official/${officialToDelete.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        fetchOfficials();
        setShowDeleteModal(false);
        setOfficialToDelete(null);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error deleting official:', error);
      setError('Failed to delete official');
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    setSelectedOfficial(null);
  };

  const handleModalSuccess = () => {
    fetchOfficials();
    handleModalClose();
  };

  const getGenderText = (gender: string) => {
    return gender === 'M' ? 'Male' : 'Female';
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (official: Official) => (
        <div className="flex items-center">
          {official.profile ? (
            <img 
              src={official.profile} 
              alt={official.name}
              className="h-8 w-8 rounded-full mr-3 object-cover"
            />
          ) : (
            <UserIcon className="h-8 w-8 text-gray-400 mr-3" />
          )}
          <div>
            <div className="font-medium text-white">{official.name}</div>
            <div className="text-sm text-gray-400">{getGenderText(official.jenis_kelamin)}</div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (official: Official) => (
        <div className="text-sm">
          <div className="text-white">{official.no_hp}</div>
          <div className="text-gray-400">{official.agama}</div>
        </div>
      )
    },
    {
      key: 'alamat',
      label: 'Address',
      render: (official: Official) => (
        <div className="text-sm text-white max-w-xs truncate">
          {official.alamat}
        </div>
      )
    },
    {
      key: 'created_at',
      label: 'Added',
      render: (official: Official) => (
        <div className="text-sm text-gray-400">
          {new Date(official.created_at).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (official: Official) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => handleEdit(official)}
          >
            <PencilIcon className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(official)}
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">My Officials</h1>
            <p className="text-gray-400 mt-1">Manage your team officials</p>
          </div>
          <Button
            onClick={() => setShowModal(true)}
            className="bg-gold-500 hover:bg-gold-600 text-black"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Official
          </Button>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search officials by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Officials Table */}
        <Card>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <Table
              columns={columns}
              data={officials}
              pagination={pagination}
              onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
            />
          )}
        </Card>

        {/* Modals */}
        <OfficialModal
          isOpen={showModal}
          onClose={handleModalClose}
          onSuccess={handleModalSuccess}
          official={selectedOfficial}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
          title="Delete Official"
          message={`Are you sure you want to delete ${officialToDelete?.name}? This action cannot be undone.`}
        />
      </div>
    </DashboardLayout>
  );
};

export default MyOfficialsPage;
