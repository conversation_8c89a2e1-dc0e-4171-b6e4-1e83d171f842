'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import TeamModal from '@/components/modals/TeamModal';
import { PlusIcon, PencilIcon, UsersIcon, UserGroupIcon } from '@heroicons/react/24/outline';

interface Kontingen {
  id: number;
  name: string;
  negara: string;
  provinsi: string;
  kabupaten_kota: string;
  atletCount: number;
  officialCount: number;
  created_at: string;
  updated_at: string;
}

const MyTeamPage = () => {
  const { user } = useAuth();
  const [kontingen, setKontingen] = useState<Kontingen | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchMyKontingen();
  }, []);

  const fetchMyKontingen = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/kontingen/my`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setKontingen(data.data);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error fetching kontingen:', error);
      setError('Failed to fetch team data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">My Team</h1>
            <p className="text-gray-400 mt-1">Manage your team (kontingen) information</p>
          </div>
          {kontingen && (
            <Button
              onClick={() => setShowModal(true)}
              className="bg-gold-500 hover:bg-gold-600 text-black"
            >
              <PencilIcon className="h-5 w-5 mr-2" />
              Edit Team
            </Button>
          )}
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {!kontingen && !error ? (
          <Card className="p-8 text-center">
            <UserGroupIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Team Registered</h3>
            <p className="text-gray-400 mb-6">
              You haven't registered your team yet. Create your team to start managing athletes and officials.
            </p>
            <Button
              onClick={() => setShowModal(true)}
              className="bg-gold-500 hover:bg-gold-600 text-black"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Team
            </Button>
          </Card>
        ) : kontingen && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Team Information */}
            <div className="lg:col-span-2">
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Team Information</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Team Name</label>
                    <p className="text-white text-lg">{kontingen.name}</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Country</label>
                      <p className="text-white">{kontingen.negara}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Province</label>
                      <p className="text-white">{kontingen.provinsi}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">City/Regency</label>
                      <p className="text-white">{kontingen.kabupaten_kota}</p>
                    </div>
                  </div>
                  <div className="pt-4 border-t border-gray-700">
                    <p className="text-sm text-gray-400">
                      Created: {new Date(kontingen.created_at).toLocaleDateString()}
                    </p>
                    <p className="text-sm text-gray-400">
                      Last Updated: {new Date(kontingen.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </Card>
            </div>

            {/* Team Statistics */}
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Team Statistics</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center">
                      <UsersIcon className="h-8 w-8 text-blue-400 mr-3" />
                      <div>
                        <p className="text-white font-medium">Athletes</p>
                        <p className="text-sm text-gray-400">Registered athletes</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-blue-400">{kontingen.atletCount}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-8 w-8 text-green-400 mr-3" />
                      <div>
                        <p className="text-white font-medium">Officials</p>
                        <p className="text-sm text-gray-400">Team officials</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-green-400">{kontingen.officialCount}</span>
                  </div>
                </div>
              </Card>

              {/* Quick Actions */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Button
                    href="/dashboard/my-athletes"
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white justify-start"
                  >
                    <UsersIcon className="h-5 w-5 mr-2" />
                    Manage Athletes
                  </Button>
                  <Button
                    href="/dashboard/my-officials"
                    className="w-full bg-green-500 hover:bg-green-600 text-white justify-start"
                  >
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    Manage Officials
                  </Button>
                  <Button
                    href="/dashboard/event-registration"
                    className="w-full bg-purple-500 hover:bg-purple-600 text-white justify-start"
                  >
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Register for Events
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* Team Modal */}
        <TeamModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onSuccess={fetchMyKontingen}
          team={kontingen}
        />
      </div>
    </DashboardLayout>
  );
};

export default MyTeamPage;
